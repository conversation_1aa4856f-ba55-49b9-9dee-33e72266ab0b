import { config } from './config';
import { logger } from './utils/logger';
import App from './app';
import { createServer } from 'http';
import { WebSocketService } from './services/websocket';

// Handle uncaught exceptions (with logging only, no exit)
process.on('uncaughtException', (error: Error) => {
  logger.error('Uncaught Exception', {
    message: error.message,
    stack: error.stack,
  });
  // Don't exit immediately - let the application handle it
});

// Handle unhandled promise rejections (with logging only, no exit)
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  logger.error('Unhandled Rejection', {
    reason: reason?.message || reason,
    stack: reason?.stack,
    promise: promise.toString(),
  });
  // Don't exit immediately - let the application handle it
});

// Handle SIGTERM signal
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  await gracefulShutdown();
});

// Handle SIGINT signal (Ctrl+C)
process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  await gracefulShutdown();
});

let server: any;
let app: App | undefined;
let webSocketService: WebSocketService | undefined;

async function startServer(): Promise<void> {
  try {
    // Create and initialize app
    app = new App();
    await app.initialize();

    // Create HTTP server
    server = createServer(app.app);

    // Initialize WebSocket service (temporarily disabled for testing)
    // webSocketService = new WebSocketService(server);

    // Start HTTP server
    logger.info(`Attempting to start server on port ${config.PORT}...`);
    server.listen(config.PORT, '0.0.0.0', () => {
      logger.info(`🚀 Server running on port ${config.PORT}`);
      logger.info(`📚 API Documentation: http://localhost:${config.PORT}/api/${config.API_VERSION}/docs`);
      logger.info(`🏥 Health Check: http://localhost:${config.PORT}/health`);
      logger.info(`🌍 Environment: ${config.NODE_ENV}`);
      logger.info(`🔌 WebSocket service temporarily disabled`);
    });

    // Handle server errors
    server.on('error', (error: any) => {
      logger.error('Server error occurred', { error: error.message, code: error.code });

      if (error.syscall !== 'listen') {
        throw error;
      }

      const bind = typeof config.PORT === 'string'
        ? 'Pipe ' + config.PORT
        : 'Port ' + config.PORT;

      switch (error.code) {
        case 'EACCES':
          logger.error(`${bind} requires elevated privileges`);
          process.exit(1);
          break;
        case 'EADDRINUSE':
          logger.error(`${bind} is already in use`);
          process.exit(1);
          break;
        default:
          logger.error(`Server error: ${error.code} - ${error.message}`);
          throw error;
      }
    });

    // Set server timeout
    server.timeout = 30000; // 30 seconds

  } catch (error) {
    logger.error('Failed to start server', { error });
    process.exit(1);
  }
}

async function gracefulShutdown(): Promise<void> {
  try {
    logger.info('Starting graceful shutdown...');

    // Stop accepting new connections
    if (server) {
      server.close((error: any) => {
        if (error) {
          logger.error('Error closing server', { error });
        } else {
          logger.info('✅ HTTP server closed');
        }
      });
    }

    // Shutdown application
    if (app) {
      await app.shutdown();
    }

    logger.info('✅ Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('❌ Error during graceful shutdown', { error });
    process.exit(1);
  }
}

// Start the server
startServer().catch((error) => {
  logger.error('Failed to start application', { error });
  process.exit(1);
});

export default app || null;
